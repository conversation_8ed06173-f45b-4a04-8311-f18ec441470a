{"name": "qiwu-zhichuang-frontend", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^6.0.0", "@tailwindcss/postcss7-compat": "^2.2.17", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.5.0", "axios": "^1.4.0", "framer-motion": "^12.12.1", "gsap": "^3.13.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "moment": "^2.30.1", "postcss-import": "^12.0.1", "postcss-loader": "^8.1.1", "react": "^18.2.0", "react-countup": "^6.5.3", "react-dom": "^18.2.0", "react-i18next": "^15.5.2", "react-intersection-observer": "^9.16.0", "react-router-dom": "^6.11.2", "react-scripts": "5.0.1", "react-webcam": "^7.1.1", "recharts": "^2.15.3", "web-vitals": "^2.1.4", "qiwu-zhichuang-frontend": "file:"}, "scripts": {"start": "react-scripts start --host 0.0.0.0", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "postcss": {"plugins": {"tailwindcss": {}, "autoprefixer": {}}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^9.8.8", "postcss": "^7.0.39", "tailwindcss": "^4.1.7"}}