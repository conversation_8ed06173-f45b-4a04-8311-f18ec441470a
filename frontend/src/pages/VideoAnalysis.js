import React, { useState, useEffect, useRef } from 'react';
import {
  Upload, Button, Select, Card, Spin, Tabs,
  message, Row, Col, Divider, List, Tag, Progress, Timeline
} from 'antd';
import { UploadOutlined, CheckCircleOutlined, CloseCircleOutlined, PlayCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import MainLayout from '../components/MainLayout';
import AngleDataVisualization from '../components/visualization/AngleDataVisualization';
import VideoAnnotation from '../components/VideoAnnotation';

const { Option } = Select;
const { TabPane } = Tabs;

const VideoAnalysis = () => {
  const { t } = useTranslation();
  const [file, setFile] = useState(null);
  const [posture, setPosture] = useState('弓步冲拳');
  const [loading, setLoading] = useState(false);
  const [analyzing, setAnalyzing] = useState(false);
  const [result, setResult] = useState(null);
  const [poses, setPoses] = useState([]);
  const [fileList, setFileList] = useState([]); // 新增：用于跟踪上传文件列表
  const [videoUrl, setVideoUrl] = useState('');
  const [videoId, setVideoId] = useState('');
  const videoRef = useRef(null);
  const [isAdmin, setIsAdmin] = useState(false); // 判断当前用户是否为管理员
  const [activeTab, setActiveTab] = useState('1'); // 当前活动的标签页

  useEffect(() => {
    // Fetch available poses
    const fetchPoses = async () => {
      try {
        const response = await axios.get('/api/poses');
        if (response.data.success) {
          setPoses(response.data.poses);
        }
      } catch (error) {
        console.error('Error fetching poses:', error);
        message.error(t('videoAnalysis.uploadCard.fetchPosesFailed'));
      }
    };

    // 检查用户角色
    const checkUserRole = async () => {
      try {
        const token = localStorage.getItem('token');
        if (token) {
          const response = await axios.get('/api/user', {
            headers: {
              Authorization: `Bearer ${token}`
            }
          });
          if (response.data.success) {
            setIsAdmin(response.data.user.role === 'admin');
          }
        }
      } catch (error) {
        console.error(t('videoAnalysis.uploadCard.checkRoleFailed'), error);
      }
    };

    fetchPoses();
    checkUserRole();
  }, []);

  // 完全重写文件上传处理函数
  const handleFileChange = (info) => {
    console.log('File change event:', info);

    // 更新文件列表状态
    setFileList(info.fileList.slice(-1)); // 只保留最新的一个文件

    // 当有文件被选择时
    if (info.fileList.length > 0) {
      const latestFile = info.fileList[info.fileList.length - 1];
      console.log('Latest file:', latestFile);

      // 设置文件状态
      if (latestFile.originFileObj) {
        setFile(latestFile.originFileObj);

        // 生成一个临时的唯一ID作为视频ID
        const tempVideoId = `video_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
        setVideoId(tempVideoId);

        // 创建视频URL用于播放
        const videoURL = URL.createObjectURL(latestFile.originFileObj);
        setVideoUrl(videoURL);

        message.success(t('videoAnalysis.uploadCard.fileSelected', { filename: latestFile.name }));
      }
    } else {
      // 当没有文件时
      setFile(null);
      setVideoUrl('');
      setVideoId('');
    }
  };

  const handlePostureChange = (value) => {
    setPosture(value);
  };

  const handleAnalyze = async () => {
    // 检查文件是否存在
    if (!file) {
      message.warning(t('videoAnalysis.uploadCard.pleaseUploadFirst'));
      return;
    }

    setAnalyzing(true);
    setLoading(true);

    try {
      // Create form data
      const formData = new FormData();
      formData.append('video', file);
      formData.append('posture', posture);

      // 添加调试信息
      console.log('Sending video analysis request with:', { fileName: file.name, fileSize: file.size, posture });

      // Send request to backend
      const response = await axios.post('/api/analysis/video', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      // 添加调试信息
      console.log('Received response:', response.data);

      if (response.data.success) {
        // 修复这里：直接使用response.data而不是results字段
        setResult({
          average_score: response.data.average_score,
          frame_scores: response.data.frame_scores,
          key_frames: response.data.key_frames,
          feedback: response.data.feedback,
          angle_data: response.data.angle_data // 添加角度数据
        });
        message.success(t('videoAnalysis.resultCard.analysisComplete'));
      } else {
        message.error(response.data.message || t('videoAnalysis.resultCard.analysisFailed'));
      }
    } catch (error) {
      console.error('Analysis error:', error);
      message.error(error.response?.data?.message || t('videoAnalysis.resultCard.analysisRequestFailed'));
    } finally {
      setAnalyzing(false);
      setLoading(false);
    }
  };

  const getScoreColor = (score) => {
    if (score >= 8) return '#52c41a';
    if (score >= 6) return '#faad14';
    return '#f5222d';
  };

  const handleAnnotationSelected = (timestamp) => {
    // 当用户点击批注时，跳转到对应时间点
    if (videoRef.current) {
      videoRef.current.currentTime = timestamp / 1000;
    }
  };

  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  // 新增：AI教练建议功能
  const generateAICoachSuggestions = (result) => {
    if (!result) return [];

    const suggestions = [];
    const avgScore = result.average_score;

    if (avgScore < 6) {
      suggestions.push({
        type: 'improvement',
        title: '基础动作练习',
        description: '建议先练习基础动作，掌握正确的身体姿态',
        priority: 'high'
      });
    } else if (avgScore < 8) {
      suggestions.push({
        type: 'refinement',
        title: '动作细节优化',
        description: '整体动作不错，可以关注细节的完善',
        priority: 'medium'
      });
    } else {
      suggestions.push({
        type: 'advanced',
        title: '进阶技巧学习',
        description: '动作已经很标准，可以尝试更高难度的技巧',
        priority: 'low'
      });
    }

    return suggestions;
  };

  return (
    <MainLayout>
      <Card title={t('videoAnalysis.title')} bordered={false}>
        <p>
          {t('videoAnalysis.description')}
        </p>

        <Row gutter={[16, 16]}>
          <Col xs={24} md={8}>
            <Card title={t('videoAnalysis.uploadCard.title')} bordered={false}>
              <Upload
                name="video"
                listType="picture"
                fileList={fileList}
                onChange={handleFileChange}
                beforeUpload={() => false} // 阻止自动上传
                accept="video/*"
              >
                <Button icon={<UploadOutlined />}>{t('videoAnalysis.uploadCard.selectVideo')}</Button>
              </Upload>

              {videoUrl && (
                <div style={{ marginTop: 16 }}>
                  <video
                    ref={videoRef}
                    src={videoUrl}
                    controls
                    style={{ width: '100%' }}
                  />
                </div>
              )}

              <Divider />

              <Select
                placeholder={t('videoAnalysis.uploadCard.selectPosePlaceholder')}
                style={{ width: '100%', marginBottom: 16 }}
                value={posture}
                onChange={handlePostureChange}
              >
                {poses.map(pose => (
                  <Option key={pose} value={pose}>{t(`imageAnalysis.poses.${pose}`)}</Option>
                ))}
              </Select>

              <Button
                type="primary"
                onClick={handleAnalyze}
                disabled={!file || analyzing}
                loading={analyzing}
                block
              >
                {t('videoAnalysis.uploadCard.startAnalysis')}
              </Button>
            </Card>
          </Col>

          <Col xs={24} md={16}>
            {loading ? (
              <Card bordered={false}>
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <Spin size="large" />
                  <p style={{ marginTop: 16 }}>{t('videoAnalysis.resultCard.analyzing')}</p>
                </div>
              </Card>
            ) : (
              <Card bordered={false}>
                <Tabs activeKey={activeTab} onChange={handleTabChange}>
                  <TabPane tab={t('videoAnalysis.resultCard.tabs.analysisResult')} key="1">
                    {result ? (
                      <div>
                        <Progress
                          percent={result.average_score * 10}
                          status="active"
                          strokeColor={getScoreColor(result.average_score)}
                        />

                        <Tag
                          color={getScoreColor(result.average_score)}
                          style={{ fontSize: 16, padding: '4px 8px', margin: '8px 0' }}
                        >
                          {result.feedback.level}
                        </Tag>

                        <Divider>{t('videoAnalysis.resultCard.evaluationTitle')}</Divider>

                        <List
                          className="feedback-list"
                          itemLayout="horizontal"
                          dataSource={result.feedback.suggestions}
                          renderItem={item => (
                            <List.Item>
                              <List.Item.Meta
                                avatar={
                                  result.average_score >= 8
                                    ? <CheckCircleOutlined style={{ color: '#52c41a', fontSize: 18 }} />
                                    : <CloseCircleOutlined style={{ color: '#f5222d', fontSize: 18 }} />
                                }
                                description={item}
                              />
                            </List.Item>
                          )}
                        />
                      </div>
                    ) : (
                      <div style={{ textAlign: 'center', padding: '40px 0' }}>
                        <p>{t('videoAnalysis.resultCard.uploadPrompt')}</p>
                      </div>
                    )}
                  </TabPane>
                  <TabPane tab={t('videoAnalysis.resultCard.tabs.annotations')} key="2">
                    {videoUrl ? (
                      <VideoAnnotation
                        videoUrl={videoUrl}
                        videoId={videoId}
                        videoRef={videoRef}
                        isAdmin={isAdmin}
                        onAnnotationSelected={handleAnnotationSelected}
                      />
                    ) : (
                      <div style={{ textAlign: 'center', padding: '40px 0' }}>
                        <p>{t('videoAnalysis.resultCard.pleaseUploadVideo')}</p>
                      </div>
                    )}
                  </TabPane>
                </Tabs>
              </Card>
            )}
          </Col>
        </Row>

        {result && result.key_frames && result.key_frames.length > 0 && (
          <Row style={{ marginTop: 16 }}>
            <Col span={24}>
              <Card title={t('videoAnalysis.keyFrames.title')} bordered={false}>
                <Row gutter={[16, 16]}>
                  {result.key_frames.map((frame, index) => (
                    <Col xs={24} sm={12} md={8} lg={6} key={index}>
                      <Card
                        hoverable
                        cover={
                          <img
                            alt={t('videoAnalysis.keyFrames.frameTitle', { index: index + 1 })}
                            src={`data:image/jpeg;base64,${frame.image}`}
                          />
                        }
                      >
                        <Card.Meta
                          title={t('videoAnalysis.keyFrames.frameTitle', { index: index + 1 })}
                          description={
                            <>
                              <p>{t('videoAnalysis.keyFrames.time')}: {frame.time.toFixed(2)}{t('videoAnalysis.keyFrames.seconds')}</p>
                              <p>{t('videoAnalysis.keyFrames.score')}: {frame.score.toFixed(1)}</p>
                            </>
                          }
                        />
                      </Card>
                    </Col>
                  ))}
                </Row>
              </Card>
            </Col>
          </Row>
        )}

        {result && result.frame_scores && result.frame_scores.length > 0 && (
          <Row style={{ marginTop: 16 }}>
            <Col span={24}>
              <Card title={t('videoAnalysis.timeline.title')} bordered={false}>
                <Timeline mode="alternate">
                  {result.frame_scores
                    .filter((_, i) => i % 5 === 0) // Show every 5th frame to avoid clutter
                    .map((frame, index) => (
                      <Timeline.Item
                        key={index}
                        color={getScoreColor(frame.score)}
                        dot={<PlayCircleOutlined />}
                      >
                        <p>时间: {frame.time.toFixed(2)}秒</p>
                        <p>评分: {frame.score.toFixed(1)}</p>
                      </Timeline.Item>
                    ))}
                </Timeline>
              </Card>
            </Col>
          </Row>
        )}

        {result && result.angle_data && (
          <Row style={{ marginTop: 16 }}>
            <Col span={24}>
              <Card title="角度数据可视化" bordered={false}>
                <AngleDataVisualization data={result.angle_data} />
              </Card>
            </Col>
          </Row>
        )}
      </Card>
    </MainLayout>
  );
};

export default VideoAnalysis;
