/* Global Styles */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Layout Styles */
.site-layout-content {
  padding: 24px;
  background: #fff;
  min-height: 280px;
}

.logo {
  float: left;
  margin-right: 20px;
  width: 120px;
  height: 31px;
  color: white;
  font-size: 18px;
  font-weight: bold;
  line-height: 31px;
}

.header-menu {
  line-height: 64px;
}

/* Card Styles */
.analysis-card {
  margin-bottom: 24px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.analysis-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Button Styles */
.action-button {
  margin-right: 8px;
  margin-bottom: 8px;
}

/* Form Styles */
.login-form {
  max-width: 400px;
  margin: 0 auto;
}

.login-form-button {
  width: 100%;
}

/* Analysis Result Styles */
.result-container {
  margin-top: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
}

.score-display {
  font-size: 48px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 16px;
}

.score-excellent {
  color: #52c41a;
}

.score-good {
  color: #faad14;
}

.score-needs-improvement {
  color: #f5222d;
}

.feedback-list {
  margin-top: 16px;
}

/* Image Display */
.pose-image {
  max-width: 100%;
  border-radius: 8px;
  margin-bottom: 16px;
}

/* Camera View */
.camera-container {
  position: relative;
  width: 100%;
  max-width: 640px;
  margin: 0 auto;
}

.webcam {
  width: 100%;
  border-radius: 8px;
}

.camera-controls {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

/* Loading States */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* Animation Delays */
.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .site-layout-content {
    padding: 16px;
  }

  .score-display {
    font-size: 36px;
  }
}
