import React, { useState, useRef, useEffect } from 'react';
import React, { useState, useRef, useEffect } from 'react';
import { Button, Input, List, Popover, message, Select, Tag, Tooltip, Modal } from 'antd';
import { CommentOutlined, DeleteOutlined, EditOutlined, SaveOutlined, UndoOutlined, CameraOutlined, PauseOutlined } from '@ant-design/icons';
import axios from 'axios';

const { TextArea } = Input;
const { Option } = Select;

/**
 * 视频批注组件
 * @param {Object} props - 组件属性
 * @param {string} props.videoUrl - 视频URL
 * @param {string} props.videoId - 视频ID
 * @param {Object} props.videoRef - 视频DOM引用
 * @param {boolean} props.isAdmin - 是否为管理员
 * @param {function} props.onAnnotationSelected - 批注选中回调函数
 */
const VideoAnnotation = ({ videoId, fileUrl, admin = true, onAddAnnotation, existingAnnotations }) => {
    const [annotations, setAnnotations] = useState(existingAnnotations || []);
    const [loading, setLoading] = useState(false);
    const canvasRef = useRef(null);
    const contextRef = useRef(null);
    const videoRef = useRef(null);
    const [isDrawing, setIsDrawing] = useState(false);
    const [currentAnnotation, setCurrentAnnotation] = useState('');
    const [currentFrame, setCurrentFrame] = useState(null);
    const [currentColor, setCurrentColor] = useState('#FF0000'); // 默认红色
    const [lineWidth, setLineWidth] = useState(3); // 默认线宽
    const [capturedFrame, setCapturedFrame] = useState(null);
    const [isAnnotating, setIsAnnotating] = useState(false);
    // 添加一个状态来追踪绘图初始化
    const [canvasInitialized, setCanvasInitialized] = useState(false); // 是否正在批注模式
    const [videoTime, setVideoTime] = useState(0); // 当前视频时间

    // 初始化Canvas
    useEffect(() => {
        if (canvasRef.current) {
            const canvas = canvasRef.current;
            // 设置适当的原始尺寸，防止被缩放影响绘图效果
            if (!canvas.width || canvas.width < 100) {
                canvas.width = videoRef.current?.videoWidth || 640;
            }
            if (!canvas.height || canvas.height < 100) {
                canvas.height = videoRef.current?.videoHeight || 360;
            }
            
            const context = canvas.getContext('2d');
            context.lineCap = 'round';
            context.strokeStyle = currentColor;
            context.lineWidth = lineWidth;
            contextRef.current = context;
            console.log('Canvas 初始化完成，已设置绘图样式:', { 颜色: currentColor, 线宽: lineWidth });
        }
    }, [videoRef, currentColor, lineWidth]);
    
    // 监听颜色和线宽变化，及时更新绘图上下文
    useEffect(() => {
        if (contextRef.current) {
            contextRef.current.strokeStyle = currentColor;
            contextRef.current.lineWidth = lineWidth;
            console.log('绘图样式已更新:', { 颜色: currentColor, 线宽: lineWidth });
        }
    }, [currentColor, lineWidth]);

    // 获取视频当前帧
    useEffect(() => {
        if (videoRef.current) {
            const updateCurrentFrame = () => {
                setCurrentFrame(Math.floor(videoRef.current.currentTime * 1000));
                setVideoTime(videoRef.current.currentTime);
            };
            
            videoRef.current.addEventListener('timeupdate', updateCurrentFrame);
            return () => {
                videoRef.current?.removeEventListener('timeupdate', updateCurrentFrame);
            };
        }
    }, [videoRef]);
    
    // 截取当前视频帧
    const captureVideoFrame = () => {
        if (!videoRef.current) {
            message.error('视频加载失败');
            return;
        }
        
        try {
            // 首先重置画布初始化状态
            setCanvasInitialized(false);
            
            // 暂停视频
            videoRef.current.pause();
            
            // 添加调试日志
            console.log('视频尺寸:', videoRef.current.videoWidth, 'x', videoRef.current.videoHeight);
            
            // 创建一个临时canvas来截取视频帧
            const tempCanvas = document.createElement('canvas');
            // 确保设置正确的尺寸，防止宽高为0
            tempCanvas.width = videoRef.current.videoWidth || 640;
            tempCanvas.height = videoRef.current.videoHeight || 360;
            
            // 将视频帧绘制到canvas上
            const tempContext = tempCanvas.getContext('2d');
            tempContext.drawImage(videoRef.current, 0, 0, tempCanvas.width, tempCanvas.height);
            
            // 将canvas内容转为数据地址
            const frameDataUrl = tempCanvas.toDataURL('image/jpeg');
            setCapturedFrame(frameDataUrl);
            
            // ************ 完全重构画布逻辑 ************
            // 1. 首先创建新画布并替换现有画布，避免画布状态问题
            if (canvasRef.current) {
                const originalCanvas = canvasRef.current;
                const canvasParent = originalCanvas.parentNode;
                
                // 创建新画布
                const newCanvas = document.createElement('canvas');
                newCanvas.width = videoRef.current.videoWidth || 640;
                newCanvas.height = videoRef.current.videoHeight || 360;
                newCanvas.style.width = '100%';
                newCanvas.style.height = 'auto';
                newCanvas.style.maxHeight = '500px';
                newCanvas.style.cursor = 'crosshair';
                newCanvas.style.position = 'relative';
                newCanvas.style.zIndex = '2';
                newCanvas.style.backgroundColor = 'transparent';
                
                // 手动添加事件监听器
                newCanvas.addEventListener('mousedown', startDrawing);
                newCanvas.addEventListener('mousemove', draw);
                newCanvas.addEventListener('mouseup', finishDrawing);
                newCanvas.addEventListener('mouseleave', finishDrawing);
                
                // 替换原画布
                canvasParent.replaceChild(newCanvas, originalCanvas);
                canvasRef.current = newCanvas;
                
                // 重新初始化上下文
                const context = newCanvas.getContext('2d');
                context.lineCap = 'round';
                context.lineJoin = 'round';
                context.strokeStyle = currentColor;
                context.lineWidth = lineWidth;
                context.globalAlpha = 1.0;
                contextRef.current = context;
                
                // 2. 在新画布上绘制视频帧
                const img = new Image();
                img.onload = () => {
                    if (contextRef.current) {
                        // 绘制背景图片
                        contextRef.current.drawImage(img, 0, 0, newCanvas.width, newCanvas.height);
                        
                        // 初始化 - 更强大的强制激活方法
                        contextRef.current.strokeStyle = currentColor;
                        contextRef.current.lineWidth = lineWidth;
                        contextRef.current.lineCap = 'round';
                        contextRef.current.lineJoin = 'round';
                        contextRef.current.globalAlpha = 1.0;
                        
                        // 重要: 使用多个不同的绘图操作来强制激活画布
                        contextRef.current.fillStyle = 'rgba(255,0,0,0.01)';
                        contextRef.current.fillRect(1, 1, 2, 2);
                        
                        contextRef.current.strokeStyle = 'rgba(0,0,255,0.01)';
                        contextRef.current.beginPath();
                        contextRef.current.moveTo(10, 10);
                        contextRef.current.lineTo(20, 20);
                        contextRef.current.stroke();
                        
                        contextRef.current.fillStyle = 'rgba(0,255,0,0.01)';
                        contextRef.current.beginPath();
                        contextRef.current.arc(30, 30, 5, 0, Math.PI * 2);
                        contextRef.current.fill();
                        
                        // 重置回原始颜色和配置
                        contextRef.current.strokeStyle = currentColor;
                        contextRef.current.fillStyle = currentColor;
                        
                        console.log('新画布初始化完成，已绘制测试线条');
                    }
                };
                img.src = frameDataUrl;
            }
            
            // 3. 设置批注模式状态
            setIsAnnotating(true);
            
            message.success('视频帧截取成功，可以开始批注');
        } catch (error) {
            console.error('视频帧截取失败:', error);
            message.error('视频帧截取失败: ' + error.message);
        }
    };
    };
    
    // 取消批注模式
    const cancelAnnotation = () => {
        setIsAnnotating(false);
        setCapturedFrame(null);
        clearCanvas();
        setCurrentAnnotation('');
        
        // 可以选择恢复视频播放
        if (videoRef.current) {
            videoRef.current.play();
        }
    };

    // 获取批注列表
    useEffect(() => {
        if (videoId) {
            fetchAnnotations();
        }
    }, [videoId]);

    // 获取批注列表
    const fetchAnnotations = async () => {
        setLoading(true);
        try {
            const response = await axios.get(`/api/annotations/${videoId}`);
            if (response.data.success) {
                setAnnotations(response.data.annotations);
            }
        } catch (error) {
            console.error('获取批注失败:', error);
            message.error('获取批注列表失败');
        } finally {
            setLoading(false);
        }
    };

    // 添加批注
    const addAnnotation = async () => {
        if (!isAnnotating) {
            message.warning('请先截取视频帧');
            return;
        }

        try {
            let annotationData = {
                video_id: videoId,
                timestamp: currentFrame,
                time_seconds: videoTime,
                type: 'combined', // 合并的批注类型
                content: currentAnnotation
            };

            // 添加画布数据和原始视频帧
            if (canvasRef.current) {
                annotationData.drawing_data = canvasRef.current.toDataURL('image/png');
                annotationData.frame_image = capturedFrame;
            }

            const response = await axios.post('/api/annotations', annotationData);
            if (response.data.success) {
                message.success('添加批注成功');
                setCurrentAnnotation('');
                clearCanvas();
                fetchAnnotations();
                setIsAnnotating(false);
                setCapturedFrame(null);
                
                // 可以选择恢复视频播放
                if (videoRef.current) {
                    videoRef.current.play();
                }
            }
        } catch (error) {
            console.error('添加批注失败:', error);
            message.error('添加批注失败');
        }
    };

    // 删除批注
    const deleteAnnotation = async (annotationId) => {
        try {
            const response = await axios.delete(`/api/annotations/${annotationId}`);
            if (response.data.success) {
                message.success('删除批注成功');
                fetchAnnotations();
            }
        } catch (error) {
            console.error('删除批注失败:', error);
            message.error('删除批注失败');
        }
    };

    // 跳转到批注时间点
    const jumpToAnnotation = (timestamp) => {
        if (videoRef.current) {
            videoRef.current.currentTime = timestamp / 1000;
            if (onAnnotationSelected) {
                onAnnotationSelected(timestamp);
            }
        }
    };

    // 核心功能：强制初始化画布上下文
    const forceInitContext = () => {
        if (contextRef.current && canvasRef.current) {
            // 重新设置画布和上下文属性
            canvasRef.current.style.opacity = '1'; // 确保画布可见
            
            // 全新设置绘图上下文
            const ctx = canvasRef.current.getContext('2d', { willReadFrequently: true });
            ctx.globalAlpha = 1.0;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            ctx.strokeStyle = currentColor;
            ctx.lineWidth = lineWidth;
            contextRef.current = ctx;
            
            // 立即激活绘图上下文
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(1, 1);
            ctx.stroke();
            
            // 更新初始化状态
            setCanvasInitialized(true);
            
            console.log('画布已强制初始化并激活:', { 颜色: currentColor, 线宽: lineWidth });
        }
    };
    
    // 绘图相关函数
    const getMousePosition = (canvas, evt) => {
        // 获取canvas的实际尺寸和显示尺寸
        const rect = canvas.getBoundingClientRect();
        const scaleX = canvas.width / rect.width;
        const scaleY = canvas.height / rect.height;
        
        return {
            x: (evt.clientX - rect.left) * scaleX,
            y: (evt.clientY - rect.top) * scaleY
        };
    };

    const startDrawing = ({ nativeEvent }) => {
        if (!contextRef.current || !canvasRef.current) return;
        
        // 如果是第一次绘图，先强制初始化画布
        if (!canvasInitialized) {
            forceInitContext();
            console.log('开始绘图前强制初始化画布');
        }
        
        // 强制设置绘图样式，确保立即生效
        contextRef.current.lineCap = 'round';
        contextRef.current.strokeStyle = currentColor;
        contextRef.current.lineWidth = lineWidth;
        contextRef.current.globalAlpha = 1.0;
        
        // 使用纠正后的坐标
        const { x, y } = getMousePosition(canvasRef.current, nativeEvent);
        contextRef.current.beginPath();
        contextRef.current.moveTo(x, y);
        setIsDrawing(true);

        // 立即绘制一个点，确保画布引擎已激活
        contextRef.current.lineTo(x+0.1, y+0.1);
        contextRef.current.stroke();
        contextRef.current.beginPath();
        contextRef.current.moveTo(x, y);

        // 调试日志
        console.log('开始绘图坐标:', x, y, '当前样式:', { 颜色: currentColor, 线宽: lineWidth });
    };

    const draw = ({ nativeEvent }) => {
        if (!isDrawing || !contextRef.current || !canvasRef.current) return;
        
        // 在每次绘制时强制设置样式，避免被覆盖
        contextRef.current.strokeStyle = currentColor;
        contextRef.current.lineWidth = lineWidth;
        
        // 使用纠正后的坐标
        const { x, y } = getMousePosition(canvasRef.current, nativeEvent);
        contextRef.current.lineTo(x, y);
        contextRef.current.stroke();
    };

    const finishDrawing = () => {
        if (contextRef.current) {
            contextRef.current.closePath();
            setIsDrawing(false);

            // 保存当前绘图到历史记录
            if (canvasRef.current) {
                const imageData = contextRef.current.getImageData(
                    0, 0, canvasRef.current.width, canvasRef.current.height
                );
                setDrawingHistory(prev => [...prev, imageData]);
            }
        }
    };

    const clearCanvas = () => {
        if (contextRef.current && canvasRef.current) {
            contextRef.current.clearRect(
                0, 0, canvasRef.current.width, canvasRef.current.height
            );
            setDrawingHistory([]);
        }
    };

    const undoLastDrawing = () => {
        if (drawingHistory.length === 0 || !contextRef.current || !canvasRef.current) return;
        
        // 移除最后一步
        const newHistory = [...drawingHistory];
        newHistory.pop();
        setDrawingHistory(newHistory);
        
        // 清空画布
        contextRef.current.clearRect(
            0, 0, canvasRef.current.width, canvasRef.current.height
        );
        
        // 重新绘制历史记录
        if (newHistory.length > 0) {
            const lastImageData = newHistory[newHistory.length - 1];
            contextRef.current.putImageData(lastImageData, 0, 0);
        }
    };

    // 渲染批注列表项
    const renderAnnotationItem = (item) => {
        const timeFormatted = new Date(item.time_seconds * 1000).toISOString().substr(11, 8);
        
        return (
            <List.Item
                key={item.id}
                actions={[
                    <Tooltip title="跳转到此时间点">
                        <Button 
                            type="link" 
                            onClick={() => jumpToAnnotation(item.timestamp)}
                            icon={<CommentOutlined />}
                        />
                    </Tooltip>,
                    isAdmin && (
                        <Tooltip title="删除批注">
                            <Button 
                                type="link" 
                                danger 
                                onClick={() => deleteAnnotation(item.id)}
                                icon={<DeleteOutlined />}
                            />
                        </Tooltip>
                    )
                ].filter(Boolean)}
            >
                <List.Item.Meta
                    title={
                        <div>
                            <Tag color="blue">{timeFormatted}</Tag>
                            <Tag color="purple">指导批注</Tag>
                        </div>
                    }
                    description={
                        <div>
                            {/* 如果是合并类型批注，先显示绘图，再显示文字 */}
                            {(item.type === 'combined' || item.type === 'drawing') && item.drawing_data && (
                                <div style={{ marginBottom: 8 }}>
                                    <img 
                                        src={item.drawing_data} 
                                        alt="批注绘图" 
                                        style={{ maxWidth: '100%', border: '1px solid #d9d9d9' }} 
                                    />
                                </div>
                            )}
                            
                            {/* 如果有文字内容，显示文字批注 */}
                            {item.content && (
                                <div style={{ margin: '8px 0' }}>
                                    {item.content}
                                </div>
                            )}
                        </div>
                    }
                />
            </List.Item>
        );
    };

    // 管理员批注工具栏
    const renderAnnotationToolbar = () => {
        console.log('检查管理员状态:', isAdmin); // 添加调试日志
        // 临时注释掉管理员检查，让所有用户都能看到批注工具
        // if (!isAdmin) return null;

        return (
            <div className="annotation-toolbar" style={{ marginBottom: 16 }}>
                {!isAnnotating ? (
                    // 开始批注前的界面
                    <div>
                        <Button 
                            type="primary" 
                            icon={<CameraOutlined />} 
                            onClick={captureVideoFrame}
                            block
                        >
                            开始批注（截取当前帧）
                        </Button>
                    </div>
                ) : (
                    // 开始批注后的界面
                    <div>
                        <div style={{ marginBottom: 8 }}>
                            <div style={{ display: 'flex', marginBottom: 8 }}>
                                <Select
                                    value={currentColor}
                                    onChange={setCurrentColor}
                                    style={{ width: 80, marginRight: 8 }}
                                >
                                    <Option value="#ff0000">红色</Option>
                                    <Option value="#00ff00">绿色</Option>
                                    <Option value="#0000ff">蓝色</Option>
                                    <Option value="#ffff00">黄色</Option>
                                    <Option value="#ffffff">白色</Option>
                                </Select>

                                <Select
                                    value={lineWidth}
                                    onChange={setLineWidth}
                                    style={{ width: 80, marginRight: 8 }}
                                >
                                    <Option value={1}>细线</Option>
                                    <Option value={3}>中等</Option>
                                    <Option value={5}>粗线</Option>
                                </Select>

                                <Button 
                                    icon={<UndoOutlined />} 
                                    onClick={undoLastDrawing}
                                    disabled={drawingHistory.length === 0}
                                    style={{ marginRight: 8 }}
                                >
                                    撤销
                                </Button>

                                <Button 
                                    icon={<DeleteOutlined />} 
                                    onClick={clearCanvas}
                                    style={{ marginRight: 8 }}
                                >
                                    清空
                                </Button>
                            </div>
                        </div>

                        {/* 绘图画布区域 */}
                        <div 
                            style={{ 
                                position: 'relative', 
                                border: '1px solid #d9d9d9',
                                marginBottom: 8,
                                backgroundColor: '#f0f0f0',
                                display: 'flex',
                                justifyContent: 'center',
                                overflow: 'hidden'
                            }}
                        >
                            {/* 批注前显示截图 */}
                            {capturedFrame && (
                                <div 
                                    style={{
                                        position: 'absolute',
                                        top: 0,
                                        left: 0,
                                        width: '100%',
                                        height: '100%',
                                        zIndex: 1
                                    }}
                                >
                                    <img 
                                        src={capturedFrame} 
                                        alt="当前视频帧" 
                                        style={{ width: '100%', height: '100%', objectFit: 'contain' }} 
                                    />
                                </div>
                            )}
                            
                            <canvas
                                ref={canvasRef}
                                onMouseDown={startDrawing}
                                onMouseMove={draw}
                                onMouseUp={finishDrawing}
                                onMouseLeave={finishDrawing}
                                style={{
                                    display: 'block',
                                    width: '100%',
                                    height: 'auto',
                                    maxHeight: '500px',
                                    cursor: 'crosshair',
                                    position: 'relative',
                                    zIndex: 2,
                                    backgroundColor: 'transparent'
                                }}
                            />
                        </div>

                        {/* 文字批注区域 */}
                        <TextArea
                            rows={3}
                            value={currentAnnotation}
                            onChange={(e) => setCurrentAnnotation(e.target.value)}
                            placeholder="输入批注内容..."
                            style={{ marginBottom: 8 }}
                        />

                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Button 
                                type="default" 
                                onClick={cancelAnnotation}
                                style={{ marginRight: 8 }}
                            >
                                取消批注
                            </Button>
                            
                            <Button 
                                type="primary" 
                                icon={<SaveOutlined />} 
                                onClick={addAnnotation}
                            >
                                保存批注
                            </Button>
                        </div>
                    </div>
                )}
            </div>
        );
    };

    // 组件最终渲染 - 此处已在组件函数内部
    return (
        <div className="video-annotation-container">
            {renderAnnotationToolbar()}

            <List
                className="annotation-list"
                loading={loading}
                itemLayout="vertical"
                dataSource={annotations}
                renderItem={renderAnnotationItem}
                locale={{ emptyText: '暂无批注' }}
            />
        </div>
    );
};

export default VideoAnnotation;
