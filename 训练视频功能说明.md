# 训练视频功能实现说明

## 功能概述

我们已经成功实现了训练视频上传、标注和反馈功能，完整的工作流程如下：

1. **普通用户**：在预约记录中上传训练视频
2. **教练**：查看、标注和评分训练视频
3. **教练**：发布标注记录供学员查看
4. **普通用户**：查看教练的标注指导，改进训练

## 实现的功能模块

### 1. 后端API (training_video_api.py)

#### 主要接口：
- `POST /api/training-videos/upload/<appointment_id>` - 用户上传训练视频
- `GET /api/training-videos/appointment/<appointment_id>` - 获取预约的训练视频列表
- `GET /api/training-videos/coach/pending` - 教练获取待标注视频
- `POST /api/training-videos/<video_id>/annotate` - 教练保存视频标注
- `POST /api/training-videos/<video_id>/publish` - 教练发布标注
- `POST /api/training-videos/<video_id>/unpublish` - 教练取消发布标注
- `GET /api/training-videos/<video_id>/annotations` - 获取视频标注
- `DELETE /api/training-videos/<video_id>` - 删除训练视频

#### 数据结构：
```json
{
  "id": "视频唯一ID",
  "appointment_id": "预约ID",
  "user_id": "用户ID",
  "coach_id": "教练ID",
  "filename": "文件名",
  "original_filename": "原始文件名",
  "file_path": "文件路径",
  "description": "视频描述",
  "upload_time": "上传时间戳",
  "annotation_status": "标注状态(pending/annotated/published)",
  "coach_score": "教练评分(1-5)",
  "coach_feedback": "教练反馈",
  "annotation_published": "是否已发布"
}
```

### 2. 前端组件

#### TrainingVideoUpload.js
- 训练视频上传组件
- 支持拖拽上传
- 文件类型和大小验证
- 上传进度显示

#### TrainingVideoList.js
- 训练视频列表显示
- 支持用户和教练两种角色
- 显示视频状态、评分、反馈
- 集成视频播放和标注功能

#### VideoAnnotation.js (扩展)
- 原有的视频标注功能
- 新增教练评分面板
- 支持评分(1-5星)和文字反馈
- 标注发布功能

### 3. 页面集成

#### CoachAppointment.js (用户预约页面)
- 在"我的预约"中添加训练视频功能
- 已确认/已完成预约显示"上传训练视频"和"查看训练视频"按钮
- 集成视频上传和查看模态框

#### CoachDashboard.js (教练管理页面)
- 新增"训练视频标注"标签页
- 显示待标注视频列表
- 集成视频标注和评分功能

## 使用流程

### 用户端操作流程：
1. 登录系统，进入"教练预约"页面
2. 切换到"我的预约"标签
3. 找到已确认或已完成的预约
4. 点击"上传训练视频"按钮
5. 选择视频文件，填写描述，上传
6. 等待教练标注
7. 点击"查看训练视频"查看教练反馈

### 教练端操作流程：
1. 登录系统，进入"教练管理中心"
2. 切换到"训练视频标注"标签
3. 查看待标注视频列表
4. 点击"查看并标注"按钮
5. 观看视频，进行标注
6. 给出评分(1-5星)和文字反馈
7. 保存标注
8. 发布标注供学员查看

## 技术特点

1. **文件安全**：支持多种视频格式，文件大小限制100MB
2. **权限控制**：严格的用户权限验证，确保数据安全
3. **状态管理**：完整的视频标注状态流转
4. **用户体验**：直观的界面设计，操作简单明了
5. **实时更新**：标注状态实时同步更新

## 数据存储

- 训练视频文件：存储在 `uploads/training_videos/` 目录
- 视频记录：存储在 `data/training_videos.json` 文件
- 标注数据：存储在 `annotations.json` 文件

## 测试建议

1. 创建测试预约并确认
2. 上传不同格式的训练视频
3. 测试教练标注和评分功能
4. 验证标注发布和查看功能
5. 测试权限控制和错误处理

## 后续优化建议

1. 添加视频压缩功能
2. 支持视频预览缩略图
3. 添加批量操作功能
4. 优化大文件上传体验
5. 添加视频播放统计
