<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        button { margin: 5px; padding: 10px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; white-space: pre-wrap; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>前端API测试</h1>
    
    <div class="section">
        <h2>1. 管理员登录</h2>
        <button onclick="loginAdmin()">登录管理员</button>
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="section">
        <h2>2. 获取预约列表</h2>
        <button onclick="getPendingAppointments()">获取待审核预约</button>
        <button onclick="getApprovedAppointments()">获取已通过预约</button>
        <button onclick="getRejectedAppointments()">获取已拒绝预约</button>
        <button onclick="getRevokedAppointments()">获取已撤销预约</button>
        <div id="appointmentsResult" class="result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5000';
        let authToken = '';

        function log(message, isError = false) {
            console.log(message);
            const resultDiv = document.getElementById('appointmentsResult');
            resultDiv.innerHTML += `<div class="${isError ? 'error' : 'success'}">${new Date().toLocaleTimeString()}: ${message}</div>`;
        }

        function logLogin(message, isError = false) {
            console.log(message);
            const resultDiv = document.getElementById('loginResult');
            resultDiv.innerHTML = `<div class="${isError ? 'error' : 'success'}">${message}</div>`;
        }

        async function loginAdmin() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin'
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    authToken = data.access_token;
                    logLogin(`登录成功！Token: ${authToken.substring(0, 50)}...`);
                } else {
                    logLogin(`登录失败: ${data.message}`, true);
                }
            } catch (error) {
                logLogin(`登录错误: ${error.message}`, true);
            }
        }

        async function makeAuthenticatedRequest(url) {
            if (!authToken) {
                log('请先登录管理员账户', true);
                return;
            }

            try {
                log(`发送请求到: ${url}`);
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                log(`响应状态: ${response.status}`);
                
                const data = await response.json();
                log(`响应数据: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success) {
                    log(`成功获取${data.appointments.length}条预约记录`);
                } else {
                    log(`请求失败: ${data.message}`, true);
                }
            } catch (error) {
                log(`请求错误: ${error.message}`, true);
            }
        }

        function getPendingAppointments() {
            makeAuthenticatedRequest(`${API_BASE_URL}/api/admin/appointments?status=pending`);
        }

        function getApprovedAppointments() {
            makeAuthenticatedRequest(`${API_BASE_URL}/api/admin/appointments?status=approved`);
        }

        function getRejectedAppointments() {
            makeAuthenticatedRequest(`${API_BASE_URL}/api/admin/appointments?status=rejected`);
        }

        function getRevokedAppointments() {
            makeAuthenticatedRequest(`${API_BASE_URL}/api/admin/appointments?status=revoked`);
        }
    </script>
</body>
</html>
